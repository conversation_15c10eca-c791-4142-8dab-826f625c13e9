import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/class_provider.dart';
import '../providers/student_provider.dart';
import '../providers/subject_provider.dart';
import '../providers/mark_provider.dart';
import '../models/class_model.dart';
import '../models/student.dart';
import '../models/subject.dart';
import '../models/mark.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({Key? key}) : super(key: key);

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ClassProvider>().loadClasses();
      context.read<StudentProvider>().loadStudents();
      context.read<SubjectProvider>().loadSubjects();
      context.read<MarkProvider>().loadMarks();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Rapports'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        automaticallyImplyLeading: false,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: GridView.count(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          children: [
            _buildReportCard(
              context,
              'Bulletin de classe',
              Icons.class_,
              Colors.blue,
              () => _showClassBulletinDialog(context),
            ),
            _buildReportCard(
              context,
              'Rapport individuel',
              Icons.person,
              Colors.green,
              () => _showStudentReportDialog(context),
            ),
            _buildReportCard(
              context,
              'Statistiques',
              Icons.analytics,
              Colors.orange,
              () => _showStatisticsDialog(context),
            ),
            _buildReportCard(
              context,
              'Moyennes',
              Icons.trending_up,
              Colors.purple,
              () => _showAveragesDialog(context),
            ),
            _buildReportCard(
              context,
              'Graphiques',
              Icons.bar_chart,
              Colors.teal,
              () => _showChartsDialog(context),
            ),
            _buildReportCard(
              context,
              'Export PDF',
              Icons.picture_as_pdf,
              Colors.red,
              () => _showExportDialog(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: color,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showClassBulletinDialog(BuildContext context) {
    final classProvider = context.read<ClassProvider>();
    if (classProvider.classes.isEmpty) {
      _showNoDataDialog(context, 'Aucune classe trouvée');
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Bulletin de classe'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Sélectionnez une classe pour générer son bulletin:'),
            const SizedBox(height: 16),
            DropdownButton<ClassModel>(
              isExpanded: true,
              hint: const Text('Choisir une classe'),
              items: classProvider.classes.map((classModel) {
                return DropdownMenuItem(
                  value: classModel,
                  child:
                      Text('${classModel.name} (${classModel.level.fullName})'),
                );
              }).toList(),
              onChanged: (classModel) {
                Navigator.of(context).pop();
                if (classModel != null) {
                  _generateClassBulletin(context, classModel);
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
        ],
      ),
    );
  }

  void _showStudentReportDialog(BuildContext context) {
    final studentProvider = context.read<StudentProvider>();
    if (studentProvider.students.isEmpty) {
      _showNoDataDialog(context, 'Aucun élève trouvé');
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Rapport individuel'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Sélectionnez un élève pour générer son rapport:'),
            const SizedBox(height: 16),
            DropdownButton<Student>(
              isExpanded: true,
              hint: const Text('Choisir un élève'),
              items: studentProvider.students.map((student) {
                return DropdownMenuItem(
                  value: student,
                  child: Text(student.fullName),
                );
              }).toList(),
              onChanged: (student) {
                Navigator.of(context).pop();
                if (student != null) {
                  _generateStudentReport(context, student);
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
        ],
      ),
    );
  }

  void _showStatisticsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Consumer4<ClassProvider, StudentProvider,
          SubjectProvider, MarkProvider>(
        builder: (context, classProvider, studentProvider, subjectProvider,
            markProvider, child) {
          final totalClasses = classProvider.classes.length;
          final totalStudents = studentProvider.students.length;
          final totalSubjects = subjectProvider.subjects.length;
          final totalMarks = markProvider.marks.length;

          double averageGrade = 0;
          if (markProvider.marks.isNotEmpty) {
            averageGrade = markProvider.marks
                    .map((mark) => mark.percentage)
                    .reduce((a, b) => a + b) /
                markProvider.marks.length;
          }

          return AlertDialog(
            title: const Text('Statistiques générales'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildStatRow('Classes:', totalClasses.toString()),
                _buildStatRow('Élèves:', totalStudents.toString()),
                _buildStatRow('Matières:', totalSubjects.toString()),
                _buildStatRow('Notes:', totalMarks.toString()),
                _buildStatRow(
                    'Moyenne générale:', '${averageGrade.toStringAsFixed(1)}%'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Fermer'),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  void _showAveragesDialog(BuildContext context) {
    final markProvider = context.read<MarkProvider>();
    final studentProvider = context.read<StudentProvider>();

    if (markProvider.marks.isEmpty) {
      _showNoDataDialog(context, 'Aucune note trouvée');
      return;
    }

    // Calculate averages by student
    Map<int, List<Mark>> marksByStudent = {};
    for (var mark in markProvider.marks) {
      marksByStudent.putIfAbsent(mark.studentId, () => []).add(mark);
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Moyennes par élève'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView.builder(
            itemCount: marksByStudent.keys.length,
            itemBuilder: (context, index) {
              final studentId = marksByStudent.keys.elementAt(index);
              final studentMarks = marksByStudent[studentId]!;
              final student = studentProvider.getStudentById(studentId);

              double average = studentMarks
                      .map((mark) => mark.percentage)
                      .reduce((a, b) => a + b) /
                  studentMarks.length;

              return ListTile(
                title: Text(student?.fullName ?? 'Élève inconnu'),
                subtitle: Text('${studentMarks.length} notes'),
                trailing: Text(
                  '${average.toStringAsFixed(1)}%',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _getGradeColor(average),
                  ),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  void _showChartsDialog(BuildContext context) {
    final markProvider = context.read<MarkProvider>();

    if (markProvider.marks.isEmpty) {
      _showNoDataDialog(
          context, 'Aucune note trouvée pour générer des graphiques');
      return;
    }

    // Prepare data for grade distribution chart
    Map<String, int> gradeDistribution = {
      'Excellent (90-100%)': 0,
      'Très bien (80-89%)': 0,
      'Bien (70-79%)': 0,
      'Assez bien (60-69%)': 0,
      'Passable (50-59%)': 0,
      'Insuffisant (0-49%)': 0,
    };

    for (var mark in markProvider.marks) {
      if (mark.percentage >= 90) {
        gradeDistribution['Excellent (90-100%)'] =
            gradeDistribution['Excellent (90-100%)']! + 1;
      } else if (mark.percentage >= 80) {
        gradeDistribution['Très bien (80-89%)'] =
            gradeDistribution['Très bien (80-89%)']! + 1;
      } else if (mark.percentage >= 70) {
        gradeDistribution['Bien (70-79%)'] =
            gradeDistribution['Bien (70-79%)']! + 1;
      } else if (mark.percentage >= 60) {
        gradeDistribution['Assez bien (60-69%)'] =
            gradeDistribution['Assez bien (60-69%)']! + 1;
      } else if (mark.percentage >= 50) {
        gradeDistribution['Passable (50-59%)'] =
            gradeDistribution['Passable (50-59%)']! + 1;
      } else {
        gradeDistribution['Insuffisant (0-49%)'] =
            gradeDistribution['Insuffisant (0-49%)']! + 1;
      }
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Distribution des notes'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: Column(
            children: gradeDistribution.entries.map((entry) {
              return ListTile(
                title: Text(entry.key),
                trailing: Text('${entry.value} notes'),
              );
            }).toList(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  void _showExportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export PDF'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Choisissez le type de rapport à exporter:'),
            SizedBox(height: 16),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Export PDF sera disponible prochainement'),
                ),
              );
            },
            child: const Text('Exporter'),
          ),
        ],
      ),
    );
  }

  void _showNoDataDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Aucune donnée'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _generateClassBulletin(BuildContext context, ClassModel classModel) {
    final studentProvider = context.read<StudentProvider>();
    final markProvider = context.read<MarkProvider>();

    final classStudents = studentProvider.students
        .where((student) => student.classId == classModel.id)
        .toList();

    if (classStudents.isEmpty) {
      _showNoDataDialog(context, 'Aucun élève dans cette classe');
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Bulletin - ${classModel.name}'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView.builder(
            itemCount: classStudents.length,
            itemBuilder: (context, index) {
              final student = classStudents[index];
              final studentMarks = markProvider.marks
                  .where((mark) => mark.studentId == student.id)
                  .toList();

              double average = 0;
              if (studentMarks.isNotEmpty) {
                average = studentMarks
                        .map((mark) => mark.percentage)
                        .reduce((a, b) => a + b) /
                    studentMarks.length;
              }

              return ListTile(
                title: Text(student.fullName),
                subtitle: Text('${studentMarks.length} notes'),
                trailing: Text(
                  average > 0
                      ? '${average.toStringAsFixed(1)}%'
                      : 'Aucune note',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: average > 0 ? _getGradeColor(average) : Colors.grey,
                  ),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  void _generateStudentReport(BuildContext context, Student student) {
    final markProvider = context.read<MarkProvider>();
    final subjectProvider = context.read<SubjectProvider>();

    final studentMarks = markProvider.marks
        .where((mark) => mark.studentId == student.id)
        .toList();

    if (studentMarks.isEmpty) {
      _showNoDataDialog(context, 'Aucune note pour cet élève');
      return;
    }

    // Group marks by subject
    Map<int, List<Mark>> marksBySubject = {};
    for (var mark in studentMarks) {
      marksBySubject.putIfAbsent(mark.subjectId, () => []).add(mark);
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Rapport - ${student.fullName}'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView.builder(
            itemCount: marksBySubject.keys.length,
            itemBuilder: (context, index) {
              final subjectId = marksBySubject.keys.elementAt(index);
              final subjectMarks = marksBySubject[subjectId]!;
              final subject = subjectProvider.getSubjectById(subjectId);

              double average = subjectMarks
                      .map((mark) => mark.percentage)
                      .reduce((a, b) => a + b) /
                  subjectMarks.length;

              return ListTile(
                leading: Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: subject != null
                        ? Color(
                            int.parse(subject.color.replaceFirst('#', '0xFF')))
                        : Colors.grey,
                    shape: BoxShape.circle,
                  ),
                ),
                title: Text(subject?.name ?? 'Matière inconnue'),
                subtitle: Text('${subjectMarks.length} notes'),
                trailing: Text(
                  '${average.toStringAsFixed(1)}%',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _getGradeColor(average),
                  ),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  Color _getGradeColor(double percentage) {
    if (percentage >= 90) return Colors.green;
    if (percentage >= 80) return Colors.lightGreen;
    if (percentage >= 70) return Colors.orange;
    if (percentage >= 60) return Colors.deepOrange;
    if (percentage >= 50) return Colors.red;
    return Colors.red[900]!;
  }
}
