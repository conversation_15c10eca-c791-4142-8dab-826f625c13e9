import 'package:flutter/material.dart';

class ReportsScreen extends StatelessWidget {
  const ReportsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Rapports'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        automaticallyImplyLeading: false,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: GridView.count(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          children: [
            _buildReportCard(
              context,
              'Bulletin de classe',
              Icons.class_,
              Colors.blue,
              () => _showComingSoon(context, 'Bulletin de classe'),
            ),
            _buildReportCard(
              context,
              'Rapport individuel',
              Icons.person,
              Colors.green,
              () => _showComingSoon(context, 'Rapport individuel'),
            ),
            _buildReportCard(
              context,
              'Statistiques',
              Icons.analytics,
              Colors.orange,
              () => _showComingSoon(context, 'Statistiques'),
            ),
            _buildReportCard(
              context,
              'Moyennes',
              Icons.trending_up,
              Colors.purple,
              () => _showComingSoon(context, 'Moyennes'),
            ),
            _buildReportCard(
              context,
              'Présences',
              Icons.event_available,
              Colors.teal,
              () => _showComingSoon(context, 'Présences'),
            ),
            _buildReportCard(
              context,
              'Export PDF',
              Icons.picture_as_pdf,
              Colors.red,
              () => _showComingSoon(context, 'Export PDF'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: color,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showComingSoon(BuildContext context, String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(feature),
        content: const Text('Cette fonctionnalité sera bientôt disponible.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
