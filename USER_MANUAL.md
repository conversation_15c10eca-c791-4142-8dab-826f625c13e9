# Manuel d'Utilisation - Application Gestion de Classe

## 📚 Introduction

L'Application Gestion de Classe est une solution complète développée en Flutter pour la gestion des classes dans le système éducatif français. Elle permet aux enseignants de gérer efficacement leurs classes, élèves, matières et notes en mode hors ligne.

## 🚀 Démarrage Rapide

### Installation et Première Utilisation

1. **Lancez l'application** : Démarrez l'app via Flutter (`flutter run`) ou depuis l'APK installé
2. **Écran d'accueil** : Vous arrivez sur le tableau de bord principal
3. **Navigation** : Utilisez les onglets en bas pour naviguer entre les différentes sections

## 📋 Fonctionnalités Principales

### 1. 🏫 Gestion des Classes

#### Créer une Nouvelle Classe

1. **Accédez à l'onglet "Classes"**
2. **Appuyez sur le bouton "+" (flottant)**
3. **Remplissez le formulaire** :
   - **Nom de la classe** : Ex: "6A", "CM2-B"
   - **Niveau éducatif** : Sélectionnez parmi CP, CE1, CE2, CM1, CM2, 6ème, 5ème, 4ème, 3ème, 2nde, 1ère, Terminale
   - **Année scolaire** : Ex: "2023-2024"
   - **Nombre maximum d'élèves** : Limite de la classe
   - **Description** (optionnel) : Informations supplémentaires
4. **Validez** en appuyant sur "Créer"

#### Visualiser et Gérer les Classes

- **Liste des classes** : Toutes vos classes apparaissent avec leurs informations essentielles
- **Filtrage par cycle** : Utilisez le menu déroulant pour filtrer par Primaire, Collège, ou Lycée
- **Informations affichées** :
  - Nom de la classe et niveau
  - Année scolaire
  - Nombre d'élèves actuel / maximum
  - Description (si renseignée)

#### Accéder aux Détails d'une Classe

1. **Tapez sur une carte de classe** dans la liste
2. **Écran des détails de classe** s'ouvre avec deux onglets :

##### Onglet "Élèves de la classe"
- **Liste complète** des élèves assignés à cette classe
- **Informations élèves** : Nom, prénom, âge, niveau
- **Actions disponibles** :
  - **Voir les détails** : Tapez sur un élève pour accéder à sa fiche complète
  - **Retirer de la classe** : Menu contextuel pour transférer l'élève vers une autre classe

##### Onglet "Ajouter des élèves" ⭐ **NOUVELLE FONCTIONNALITÉ**
- **Liste des élèves disponibles** du même niveau éducatif
- **Informations détaillées** :
  - Élèves du même niveau non assignés à cette classe
  - Classe actuelle de l'élève (si applicable)
  - Âge et niveau de chaque élève
- **Ajout d'élèves** :
  - **Bouton "Ajouter"** : Ajoute directement l'élève à la classe
  - **Validation automatique** : Vérification de la capacité de la classe
  - **Messages d'information** : Confirmation des transferts

### 2. 👥 Gestion des Élèves

#### Ajouter un Nouvel Élève

**Méthode 1 : Depuis l'onglet "Élèves"**
1. **Accédez à l'onglet "Élèves"**
2. **Appuyez sur le bouton "+"**
3. **Remplissez le formulaire complet**

**Méthode 2 : Depuis une classe spécifique** ⭐ **NOUVELLE FONCTIONNALITÉ**
1. **Ouvrez les détails d'une classe**
2. **Appuyez sur le bouton "+" flottant**
3. **Formulaire pré-rempli** avec le niveau et la classe sélectionnés

#### Formulaire d'Ajout/Modification d'Élève

- **Prénom** * : Prénom de l'élève (requis)
- **Nom** * : Nom de famille de l'élève (requis)
- **Date de naissance** * : Sélection via calendrier (requis)
- **Niveau éducatif** * : CP à Terminale (requis)
- **Classe** * : Sélection automatique des classes du niveau choisi (requis)
- **Notes** : Informations supplémentaires (optionnel)

#### Transfert d'Élèves Entre Classes ⭐ **NOUVELLE FONCTIONNALITÉ**

**Processus de Transfert :**
1. **Accédez aux détails** de la classe de destination
2. **Onglet "Ajouter des élèves"** : Consultez la liste des élèves disponibles
3. **Sélectionnez un élève** d'une autre classe du même niveau
4. **Dialogue de confirmation** s'affiche avec :
   - **Informations de l'élève** : Nom, âge, niveau
   - **Détails du transfert** : Classe actuelle → Classe de destination
   - **Statut de la classe cible** : Nombre d'élèves actuel/maximum
   - **Alertes** : Si la classe est pleine
5. **Confirmez le transfert** en appuyant sur "Ajouter"

**Sécurités Intégrées :**
- **Vérification de capacité** : Impossible d'ajouter si la classe est pleine
- **Validation de niveau** : Seuls les élèves du même niveau sont proposés
- **Messages d'erreur** explicites en cas de problème

### 3. 📊 Fonctionnalités de Suivi

#### États des Classes

- **Classes pleines** : Affichage en rouge du ratio élèves/maximum
- **Capacité disponible** : Affichage normal du nombre d'élèves
- **Classes vides** : Message d'encouragement à ajouter des élèves

#### Informations en Temps Réel

- **Compteurs automatiques** : Nombre d'élèves par classe mis à jour instantanément
- **Synchronisation** : Toutes les vues se mettent à jour après chaque modification
- **Historique** : Dates de création et modification conservées

## 🔧 Fonctionnalités Avancées

### Gestion des Contraintes

- **Niveaux cohérents** : Impossible d'assigner un élève à une classe de niveau différent
- **Capacité respectée** : Contrôle automatique du nombre maximum d'élèves
- **Intégrité des données** : Validation complète avant chaque sauvegarde

### Interface Adaptative

- **Codes couleur** :
  - **Vert** : Primaire (CP-CM2)
  - **Bleu** : Collège (6ème-3ème)
  - **Violet** : Lycée (2nde-Terminale)
- **Icônes intuitives** : Chaque action a son icône dédiée
- **Messages contextuels** : Confirmations et erreurs clairement affichées

## 💡 Conseils d'Utilisation

### Workflow Recommandé

1. **Créez d'abord vos classes** pour chaque niveau que vous enseignez
2. **Ajoutez vos élèves** soit globalement, soit classe par classe
3. **Utilisez les transferts** pour équilibrer vos classes
4. **Consultez régulièrement** les détails des classes pour suivre l'évolution

### Bonnes Pratiques

#### Organisation des Classes
- **Nommage cohérent** : Utilisez des conventions claires (ex: "6A", "6B", "CM2-Rouge")
- **Descriptions utiles** : Ajoutez des informations comme "Classe bilangue" ou "Option sport"
- **Capacités réalistes** : Définissez des limites d'élèves conformes aux normes

#### Gestion des Élèves
- **Informations complètes** : Remplissez tous les champs obligatoires
- **Vérification des âges** : Assurez-vous de la cohérence âge/niveau
- **Notes personnelles** : Utilisez le champ notes pour des informations importantes

### Cas d'Usage Fréquents

#### Équilibrage des Classes
1. **Consultez les effectifs** de chaque classe du même niveau
2. **Identifiez les déséquilibres** (classes trop pleines ou vides)
3. **Transférez des élèves** via l'interface de gestion des classes
4. **Vérifiez les résultats** dans la liste générale des classes

#### Nouveau Trimestre
1. **Vérifiez les informations** de toutes vos classes
2. **Mettez à jour les descriptions** si nécessaire
3. **Contrôlez les effectifs** pour optimiser les groupes
4. **Ajoutez de nouveaux élèves** éventuels

## ⚠️ Gestion des Erreurs

### Messages d'Erreur Courants

- **"La classe est pleine"** : Réduisez d'abord l'effectif ou augmentez la capacité maximum
- **"Aucune classe disponible pour ce niveau"** : Créez une classe pour le niveau souhaité
- **"Erreur lors de la sauvegarde"** : Vérifiez votre saisie et réessayez

### Résolution de Problèmes

#### Élève Non Transférable
- **Vérifiez le niveau** : L'élève et la classe doivent avoir le même niveau éducatif
- **Contrôlez la capacité** : La classe de destination ne doit pas être pleine

#### Interface Non Réactive
- **Actualisez** en naviguant vers un autre onglet puis en revenant
- **Redémarrez l'application** si le problème persiste

## 🎯 Nouveautés de Cette Version

### ⭐ Fonctionnalité Principale : Gestion des Membres de Classe

#### Ce qui a été ajouté :
- **Écran détaillé des classes** avec onglets de navigation
- **Interface de transfert d'élèves** intuitive et sécurisée
- **Validation automatique** des contraintes de capacité et niveau
- **Messages informatifs** pour guider l'utilisateur
- **Intégration complète** avec les fonctionnalités existantes

#### Améliorations apportées :
- **Workflow optimisé** pour l'ajout d'élèves à une classe spécifique
- **Formulaires pré-remplis** depuis le contexte de la classe
- **Sécurités renforcées** contre les erreurs de manipulation
- **Interface utilisateur** plus claire et professionnelle

### Compatibilité

- **Données existantes** : Toutes vos données actuelles sont conservées
- **Fonctionnalités précédentes** : Aucune régression sur les features existantes
- **Performance** : Optimisations pour une meilleure réactivité

## 🆘 Support et Aide

### En cas de Problème
1. **Redémarrez l'application** pour résoudre les problèmes temporaires
2. **Vérifiez vos données** : Assurez-vous que les informations saisies sont correctes
3. **Consultez les messages d'erreur** : Ils contiennent souvent la solution

### Limitations Actuelles
- **Suppression d'élèves** : Les élèves retirés d'une classe sont automatiquement transférés vers une autre classe du même niveau
- **Classes sans alternatives** : Impossible de retirer un élève s'il n'y a qu'une seule classe de son niveau

## 🔄 Mises à Jour Futures

### Fonctionnalités Prévues
- **Gestion des absences** élèves
- **Export PDF** des listes de classe
- **Statistiques avancées** par classe et niveau
- **Photos d'élèves** pour faciliter l'identification
- **Mode sombre** pour l'interface utilisateur

---

**Version du Manuel :** 1.1 - Novembre 2024  
**Application :** Gestion de Classe Flutter  
**Système éducatif :** Français (Primaire, Collège, Lycée)

Ce manuel couvre toutes les fonctionnalités actuelles de l'application, avec un focus particulier sur les nouvelles capacités de gestion des membres de classe. Pour toute question supplémentaire, consultez la documentation technique ou les guides d'implémentation fournis avec l'application.
