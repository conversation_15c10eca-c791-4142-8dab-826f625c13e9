enum MarkType {
  evaluation('Évaluation'),
  devoir('Devoir'),
  controle('Contrôle'),
  examen('Examen'),
  oral('Oral'),
  projet('Projet');

  const MarkType(this.displayName);
  final String displayName;
  
  static MarkType fromString(String value) {
    return MarkType.values.firstWhere(
      (type) => type.name == value,
      orElse: () => MarkType.evaluation,
    );
  }
}

class Mark {
  final int? id;
  final int studentId;
  final int subjectId;
  final double value; // Note sur 20
  final double maxValue; // Note maximale (généralement 20)
  final MarkType type;
  final String? title;
  final String? description;
  final DateTime date;
  final DateTime createdAt;
  final DateTime updatedAt;

  Mark({
    this.id,
    required this.studentId,
    required this.subjectId,
    required this.value,
    this.maxValue = 20.0,
    required this.type,
    this.title,
    this.description,
    DateTime? date,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : date = date ?? DateTime.now(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  double get percentage => (value / maxValue) * 100;
  
  String get grade {
    final percentage = this.percentage;
    if (percentage >= 90) return 'Très bien';
    if (percentage >= 80) return 'Bien';
    if (percentage >= 70) return 'Assez bien';
    if (percentage >= 60) return 'Passable';
    if (percentage >= 50) return 'Insuffisant';
    return 'Très insuffisant';
  }
  
  String get formattedValue => '${value.toStringAsFixed(1)}/${maxValue.toStringAsFixed(0)}';

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'studentId': studentId,
      'subjectId': subjectId,
      'value': value,
      'maxValue': maxValue,
      'type': type.name,
      'title': title,
      'description': description,
      'date': date.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory Mark.fromMap(Map<String, dynamic> map) {
    return Mark(
      id: map['id'],
      studentId: map['studentId'],
      subjectId: map['subjectId'],
      value: map['value'].toDouble(),
      maxValue: map['maxValue']?.toDouble() ?? 20.0,
      type: MarkType.fromString(map['type']),
      title: map['title'],
      description: map['description'],
      date: DateTime.parse(map['date']),
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }

  Mark copyWith({
    int? id,
    int? studentId,
    int? subjectId,
    double? value,
    double? maxValue,
    MarkType? type,
    String? title,
    String? description,
    DateTime? date,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Mark(
      id: id ?? this.id,
      studentId: studentId ?? this.studentId,
      subjectId: subjectId ?? this.subjectId,
      value: value ?? this.value,
      maxValue: maxValue ?? this.maxValue,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      date: date ?? this.date,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }
}
