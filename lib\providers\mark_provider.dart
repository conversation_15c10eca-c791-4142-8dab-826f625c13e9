import 'package:flutter/foundation.dart';
import '../models/mark.dart';
import '../services/database_service.dart';

class Mark<PERSON>rovider with ChangeNotifier {
  final DatabaseService _databaseService;
  List<Mark> _marks = [];
  Mark? _selectedMark;
  bool _isLoading = false;

  MarkProvider(this._databaseService);

  List<Mark> get marks => _marks;
  Mark? get selectedMark => _selectedMark;
  bool get isLoading => _isLoading;

  Future<void> loadMarks({int? studentId, int? subjectId}) async {
    _isLoading = true;
    notifyListeners();

    try {
      _marks = await _databaseService.getMarks(
        studentId: studentId,
        subjectId: subjectId,
      );
    } catch (e) {
      debugPrint('Error loading marks: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> addMark(Mark mark) async {
    try {
      final id = await _databaseService.insertMark(mark);
      final newMark = mark.copyWith(id: id);
      _marks.add(newMark);
      _marks.sort((a, b) => b.date.compareTo(a.date)); // Sort by date descending
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error adding mark: $e');
      return false;
    }
  }

  Future<bool> updateMark(Mark mark) async {
    try {
      await _databaseService.updateMark(mark);
      final index = _marks.indexWhere((m) => m.id == mark.id);
      if (index != -1) {
        _marks[index] = mark;
        if (_selectedMark?.id == mark.id) {
          _selectedMark = mark;
        }
        _marks.sort((a, b) => b.date.compareTo(a.date));
        notifyListeners();
      }
      return true;
    } catch (e) {
      debugPrint('Error updating mark: $e');
      return false;
    }
  }

  Future<bool> deleteMark(int id) async {
    try {
      await _databaseService.deleteMark(id);
      _marks.removeWhere((m) => m.id == id);
      if (_selectedMark?.id == id) {
        _selectedMark = null;
      }
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error deleting mark: $e');
      return false;
    }
  }

  void selectMark(Mark? mark) {
    _selectedMark = mark;
    notifyListeners();
  }

  Mark? getMarkById(int id) {
    try {
      return _marks.firstWhere((m) => m.id == id);
    } catch (e) {
      return null;
    }
  }

  List<Mark> getMarksByStudent(int studentId) {
    return _marks.where((m) => m.studentId == studentId).toList();
  }

  List<Mark> getMarksBySubject(int subjectId) {
    return _marks.where((m) => m.subjectId == subjectId).toList();
  }

  List<Mark> getMarksByStudentAndSubject(int studentId, int subjectId) {
    return _marks
        .where((m) => m.studentId == studentId && m.subjectId == subjectId)
        .toList();
  }

  double getStudentAverage(int studentId, {int? subjectId}) {
    List<Mark> studentMarks;
    if (subjectId != null) {
      studentMarks = getMarksByStudentAndSubject(studentId, subjectId);
    } else {
      studentMarks = getMarksByStudent(studentId);
    }

    if (studentMarks.isEmpty) return 0.0;

    double total = 0.0;
    for (final mark in studentMarks) {
      total += (mark.value / mark.maxValue) * 20; // Normalize to /20
    }

    return total / studentMarks.length;
  }

  Map<MarkType, int> getMarkTypeDistribution(int studentId) {
    final studentMarks = getMarksByStudent(studentId);
    final distribution = <MarkType, int>{};

    for (final type in MarkType.values) {
      distribution[type] = studentMarks.where((m) => m.type == type).length;
    }

    return distribution;
  }

  List<Mark> getRecentMarks(int studentId, {int limit = 5}) {
    final studentMarks = getMarksByStudent(studentId);
    studentMarks.sort((a, b) => b.date.compareTo(a.date));
    return studentMarks.take(limit).toList();
  }

  double getSubjectAverage(int subjectId) {
    final subjectMarks = getMarksBySubject(subjectId);
    if (subjectMarks.isEmpty) return 0.0;

    double total = 0.0;
    for (final mark in subjectMarks) {
      total += (mark.value / mark.maxValue) * 20; // Normalize to /20
    }

    return total / subjectMarks.length;
  }
}
