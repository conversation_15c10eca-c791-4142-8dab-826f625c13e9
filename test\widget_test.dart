import 'package:flutter_test/flutter_test.dart';
import 'package:gestion_classe_app/models/educational_level.dart';
import 'package:gestion_classe_app/models/class_model.dart';
import 'package:gestion_classe_app/models/student.dart';
import 'package:gestion_classe_app/models/subject.dart';
import 'package:gestion_classe_app/models/mark.dart';

void main() {
  group('Educational Level Tests', () {
    test('should create educational levels correctly', () {
      expect(EducationalLevel.cp.code, 'CP');
      expect(EducationalLevel.cp.fullName, 'Cours Préparatoire');
      expect(EducationalLevel.cp.cycle, 'primaire');
      
      expect(EducationalLevel.sixieme.code, '6ème');
      expect(EducationalLevel.sixieme.cycle, 'collège');
      
      expect(EducationalLevel.terminale.code, 'Term');
      expect(EducationalLevel.terminale.cycle, 'lycée');
    });

    test('should get levels by cycle', () {
      final primairelevels = EducationalLevel.getLevelsByCycle('primaire');
      expect(primairelevels.length, 5);
      expect(primairelevels.first, EducationalLevel.cp);
      
      final collegelevels = EducationalLevel.getLevelsByCycle('collège');
      expect(collegelevels.length, 4);
      
      final lyceelevels = EducationalLevel.getLevelsByCycle('lycée');
      expect(lyceelevels.length, 3);
    });
  });

  group('Class Model Tests', () {
    test('should create class model correctly', () {
      final classModel = ClassModel(
        name: 'CM2 A',
        level: EducationalLevel.cm2,
        schoolYear: '2023-2024',
        description: 'Classe de CM2',
        maxStudents: 25,
      );

      expect(classModel.name, 'CM2 A');
      expect(classModel.level, EducationalLevel.cm2);
      expect(classModel.schoolYear, '2023-2024');
      expect(classModel.maxStudents, 25);
    });

    test('should convert to and from map', () {
      final classModel = ClassModel(
        id: 1,
        name: 'CP B',
        level: EducationalLevel.cp,
        schoolYear: '2023-2024',
      );

      final map = classModel.toMap();
      final fromMap = ClassModel.fromMap(map);

      expect(fromMap.id, classModel.id);
      expect(fromMap.name, classModel.name);
      expect(fromMap.level, classModel.level);
      expect(fromMap.schoolYear, classModel.schoolYear);
    });
  });

  group('Student Tests', () {
    test('should create student correctly', () {
      final student = Student(
        firstName: 'Jean',
        lastName: 'Dupont',
        dateOfBirth: DateTime(2015, 3, 15),
        level: EducationalLevel.cp,
        classId: 1,
      );

      expect(student.fullName, 'Jean Dupont');
      expect(student.age, DateTime.now().year - 2015);
      expect(student.level, EducationalLevel.cp);
    });

    test('should calculate age correctly', () {
      final student = Student(
        firstName: 'Marie',
        lastName: 'Martin',
        dateOfBirth: DateTime(2010, 6, 1),
        level: EducationalLevel.sixieme,
        classId: 1,
      );

      final expectedAge = DateTime.now().year - 2010;
      expect(student.age, expectedAge);
    });
  });

  group('Subject Tests', () {
    test('should create subject correctly', () {
      final subject = Subject(
        name: 'Mathématiques',
        code: 'MATH',
        level: EducationalLevel.cm2,
        coefficient: 3,
        color: '#2196F3',
      );

      expect(subject.name, 'Mathématiques');
      expect(subject.code, 'MATH');
      expect(subject.coefficient, 3);
    });

    test('should get default subjects for primaire', () {
      final subjects = Subject.getDefaultSubjects(EducationalLevel.cp);
      expect(subjects.length, 6);
      
      final mathSubject = subjects.firstWhere((s) => s.code == 'MATH');
      expect(mathSubject.name, 'Mathématiques');
      
      final frenchSubject = subjects.firstWhere((s) => s.code == 'FRAN');
      expect(frenchSubject.name, 'Français');
    });

    test('should get default subjects for collège', () {
      final subjects = Subject.getDefaultSubjects(EducationalLevel.sixieme);
      expect(subjects.length, 8);
      
      final mathSubject = subjects.firstWhere((s) => s.code == 'MATH');
      expect(mathSubject.coefficient, 4);
    });
  });

  group('Mark Tests', () {
    test('should create mark correctly', () {
      final mark = Mark(
        studentId: 1,
        subjectId: 1,
        value: 15.5,
        maxValue: 20.0,
        type: MarkType.devoir,
        title: 'Devoir de mathématiques',
      );

      expect(mark.value, 15.5);
      expect(mark.percentage, 77.5);
      expect(mark.grade, 'Assez bien');
      expect(mark.formattedValue, '15.5/20');
    });

    test('should calculate grade correctly', () {
      final excellentMark = Mark(
        studentId: 1,
        subjectId: 1,
        value: 18.0,
        type: MarkType.evaluation,
      );
      expect(excellentMark.grade, 'Très bien');

      final goodMark = Mark(
        studentId: 1,
        subjectId: 1,
        value: 16.0,
        type: MarkType.evaluation,
      );
      expect(goodMark.grade, 'Bien');

      final poorMark = Mark(
        studentId: 1,
        subjectId: 1,
        value: 8.0,
        type: MarkType.evaluation,
      );
      expect(poorMark.grade, 'Très insuffisant');
    });

    test('should handle different mark types', () {
      expect(MarkType.devoir.displayName, 'Devoir');
      expect(MarkType.controle.displayName, 'Contrôle');
      expect(MarkType.examen.displayName, 'Examen');
    });
  });
}
