import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/student.dart';
import '../models/educational_level.dart';
import '../models/class_model.dart';
import '../providers/student_provider.dart';
import '../providers/class_provider.dart';

class StudentFormDialog extends StatefulWidget {
  final Student? student;
  final ClassModel? initialClass;
  final EducationalLevel? initialLevel;

  const StudentFormDialog({
    Key? key, 
    this.student, 
    this.initialClass,
    this.initialLevel,
  }) : super(key: key);

  @override
  State<StudentFormDialog> createState() => _StudentFormDialogState();
}

class _StudentFormDialogState extends State<StudentFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _notesController = TextEditingController();

  DateTime _selectedDate = DateTime.now().subtract(const Duration(days: 365 * 8));
  EducationalLevel _selectedLevel = EducationalLevel.cp;
  ClassModel? _selectedClass;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    
    // Initialize with provided initial values
    if (widget.initialLevel != null) {
      _selectedLevel = widget.initialLevel!;
    }
    if (widget.initialClass != null) {
      _selectedClass = widget.initialClass;
      _selectedLevel = widget.initialClass!.level; // Ensure level matches class
    }
    
    if (widget.student != null) {
      _firstNameController.text = widget.student!.firstName;
      _lastNameController.text = widget.student!.lastName;
      _notesController.text = widget.student!.notes ?? '';
      _selectedDate = widget.student!.dateOfBirth;
      _selectedLevel = widget.student!.level;
      
      // Find the class
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final classProvider = context.read<ClassProvider>();
        _selectedClass = classProvider.getClassById(widget.student!.classId);
        setState(() {});
      });
    }
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.student == null ? 'Nouvel élève' : 'Modifier l\'élève'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: _firstNameController,
                  decoration: const InputDecoration(
                    labelText: 'Prénom *',
                    hintText: 'Ex: Jean',
                  ),
                  textCapitalization: TextCapitalization.words,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Le prénom est requis';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _lastNameController,
                  decoration: const InputDecoration(
                    labelText: 'Nom *',
                    hintText: 'Ex: Dupont',
                  ),
                  textCapitalization: TextCapitalization.words,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Le nom est requis';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                InkWell(
                  onTap: _selectDate,
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      labelText: 'Date de naissance *',
                      suffixIcon: Icon(Icons.calendar_today),
                    ),
                    child: Text(
                      '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<EducationalLevel>(
                  value: _selectedLevel,
                  decoration: const InputDecoration(
                    labelText: 'Niveau *',
                  ),
                  items: EducationalLevel.values.map((level) {
                    return DropdownMenuItem(
                      value: level,
                      child: Text('${level.code} - ${level.fullName}'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedLevel = value!;
                      _selectedClass = null; // Reset class selection
                    });
                  },
                ),
                const SizedBox(height: 16),
                Consumer<ClassProvider>(
                  builder: (context, classProvider, child) {
                    final availableClasses = classProvider.classes
                        .where((c) => c.level == _selectedLevel)
                        .toList();

                    if (availableClasses.isEmpty) {
                      return const InputDecorator(
                        decoration: InputDecoration(
                          labelText: 'Classe',
                        ),
                        child: Text(
                          'Aucune classe disponible pour ce niveau',
                          style: TextStyle(color: Colors.grey),
                        ),
                      );
                    }

                    return DropdownButtonFormField<ClassModel>(
                      value: _selectedClass,
                      decoration: const InputDecoration(
                        labelText: 'Classe *',
                      ),
                      items: availableClasses.map((classModel) {
                        return DropdownMenuItem(
                          value: classModel,
                          child: Text(classModel.name),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedClass = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'Veuillez sélectionner une classe';
                        }
                        return null;
                      },
                    );
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _notesController,
                  decoration: const InputDecoration(
                    labelText: 'Notes (optionnel)',
                    hintText: 'Informations supplémentaires...',
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveStudent,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(widget.student == null ? 'Créer' : 'Modifier'),
        ),
      ],
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365 * 20)),
      lastDate: DateTime.now(),
      locale: const Locale('fr', 'FR'),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _saveStudent() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedClass == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez sélectionner une classe'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final student = Student(
        id: widget.student?.id,
        firstName: _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim(),
        dateOfBirth: _selectedDate,
        level: _selectedLevel,
        classId: _selectedClass!.id!,
        notes: _notesController.text.trim().isEmpty 
            ? null 
            : _notesController.text.trim(),
        createdAt: widget.student?.createdAt,
      );

      final studentProvider = context.read<StudentProvider>();
      bool success;

      if (widget.student == null) {
        success = await studentProvider.addStudent(student);
      } else {
        success = await studentProvider.updateStudent(student);
      }

      if (success) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.student == null 
                ? 'Élève créé avec succès' 
                : 'Élève modifié avec succès'),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors de la sauvegarde'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
