@echo off
echo Gestion de Classe - Application Flutter
echo =====================================
echo.

echo Verification de Flutter...
flutter --version
if %errorlevel% neq 0 (
    echo Erreur: Flutter n'est pas installe ou n'est pas dans le PATH
    echo Veuillez installer Flutter: https://flutter.dev/docs/get-started/install
    pause
    exit /b 1
)

echo.
echo Installation des dependances...
flutter pub get
if %errorlevel% neq 0 (
    echo Erreur lors de l'installation des dependances
    pause
    exit /b 1
)

echo.
echo Verification des appareils connectes...
flutter devices

echo.
echo Lancement de l'application...
echo Assurez-vous qu'un emulateur Android est lance ou qu'un appareil est connecte
flutter run

pause
