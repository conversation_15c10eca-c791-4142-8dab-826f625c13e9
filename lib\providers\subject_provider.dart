import 'package:flutter/foundation.dart';
import '../models/subject.dart';
import '../models/educational_level.dart';
import '../services/database_service.dart';

class SubjectProvider with ChangeNotifier {
  final DatabaseService _databaseService;
  List<Subject> _subjects = [];
  Subject? _selectedSubject;
  bool _isLoading = false;

  SubjectProvider(this._databaseService) {
    loadSubjects();
  }

  List<Subject> get subjects => _subjects;
  Subject? get selectedSubject => _selectedSubject;
  bool get isLoading => _isLoading;

  Future<void> loadSubjects({String? level}) async {
    _isLoading = true;
    notifyListeners();

    try {
      _subjects = await _databaseService.getSubjects(level: level);
    } catch (e) {
      debugPrint('Error loading subjects: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> addSubject(Subject subject) async {
    try {
      final id = await _databaseService.insertSubject(subject);
      final newSubject = subject.copyWith(id: id);
      _subjects.add(newSubject);
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error adding subject: $e');
      return false;
    }
  }

  Future<bool> updateSubject(Subject subject) async {
    try {
      await _databaseService.updateSubject(subject);
      final index = _subjects.indexWhere((s) => s.id == subject.id);
      if (index != -1) {
        _subjects[index] = subject;
        if (_selectedSubject?.id == subject.id) {
          _selectedSubject = subject;
        }
        notifyListeners();
      }
      return true;
    } catch (e) {
      debugPrint('Error updating subject: $e');
      return false;
    }
  }

  Future<bool> deleteSubject(int id) async {
    try {
      await _databaseService.deleteSubject(id);
      _subjects.removeWhere((s) => s.id == id);
      if (_selectedSubject?.id == id) {
        _selectedSubject = null;
      }
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error deleting subject: $e');
      return false;
    }
  }

  void selectSubject(Subject? subject) {
    _selectedSubject = subject;
    notifyListeners();
  }

  Subject? getSubjectById(int id) {
    try {
      return _subjects.firstWhere((s) => s.id == id);
    } catch (e) {
      return null;
    }
  }

  List<Subject> getSubjectsByLevel(EducationalLevel level) {
    return _subjects.where((s) => s.level == level).toList();
  }

  Future<bool> initializeDefaultSubjects(EducationalLevel level) async {
    try {
      final defaultSubjects = Subject.getDefaultSubjects(level);
      for (final subject in defaultSubjects) {
        await addSubject(subject);
      }
      return true;
    } catch (e) {
      debugPrint('Error initializing default subjects: $e');
      return false;
    }
  }

  bool hasSubjectsForLevel(EducationalLevel level) {
    return _subjects.any((s) => s.level == level);
  }
}
