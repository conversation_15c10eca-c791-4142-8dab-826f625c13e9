import 'package:flutter/foundation.dart';
import '../models/class_model.dart';
import '../services/database_service.dart';

class ClassProvider with ChangeNotifier {
  final DatabaseService _databaseService;
  List<ClassModel> _classes = [];
  ClassModel? _selectedClass;
  bool _isLoading = false;

  ClassProvider(this._databaseService) {
    loadClasses();
  }

  List<ClassModel> get classes => _classes;
  ClassModel? get selectedClass => _selectedClass;
  bool get isLoading => _isLoading;

  Future<void> loadClasses() async {
    _isLoading = true;
    notifyListeners();

    try {
      _classes = await _databaseService.getClasses();
    } catch (e) {
      debugPrint('Error loading classes: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> addClass(ClassModel classModel) async {
    try {
      final id = await _databaseService.insertClass(classModel);
      final newClass = classModel.copyWith(id: id);
      _classes.add(newClass);
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error adding class: $e');
      return false;
    }
  }

  Future<bool> updateClass(ClassModel classModel) async {
    try {
      await _databaseService.updateClass(classModel);
      final index = _classes.indexWhere((c) => c.id == classModel.id);
      if (index != -1) {
        _classes[index] = classModel;
        if (_selectedClass?.id == classModel.id) {
          _selectedClass = classModel;
        }
        notifyListeners();
      }
      return true;
    } catch (e) {
      debugPrint('Error updating class: $e');
      return false;
    }
  }

  Future<bool> deleteClass(int id) async {
    try {
      await _databaseService.deleteClass(id);
      _classes.removeWhere((c) => c.id == id);
      if (_selectedClass?.id == id) {
        _selectedClass = null;
      }
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error deleting class: $e');
      return false;
    }
  }

  void selectClass(ClassModel? classModel) {
    _selectedClass = classModel;
    notifyListeners();
  }

  ClassModel? getClassById(int id) {
    try {
      return _classes.firstWhere((c) => c.id == id);
    } catch (e) {
      return null;
    }
  }

  List<ClassModel> getClassesByLevel(String level) {
    return _classes.where((c) => c.level.code == level).toList();
  }

  List<ClassModel> getClassesBySchoolYear(String schoolYear) {
    return _classes.where((c) => c.schoolYear == schoolYear).toList();
  }
}
