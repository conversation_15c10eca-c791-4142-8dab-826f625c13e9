import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/student.dart';
import '../models/class_model.dart';
import '../providers/student_provider.dart';
import '../providers/class_provider.dart';

class AddStudentToClassDialog extends StatefulWidget {
  final Student student;
  final ClassModel targetClass;

  const AddStudentToClassDialog({
    Key? key,
    required this.student,
    required this.targetClass,
  }) : super(key: key);

  @override
  State<AddStudentToClassDialog> createState() => _AddStudentToClassDialogState();
}

class _AddStudentToClassDialogState extends State<AddStudentToClassDialog> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Consumer2<StudentProvider, ClassProvider>(
      builder: (context, studentProvider, classProvider, child) {
        final currentClass = classProvider.getClassById(widget.student.classId);
        final currentStudentsInTarget = studentProvider.getStudentsByClass(widget.targetClass.id!);
        final isClassFull = currentStudentsInTarget.length >= widget.targetClass.maxStudents;
        
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.person_add, color: Colors.blue),
              SizedBox(width: 8),
              Text('Ajouter à la classe'),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildStudentInfo(),
                const SizedBox(height: 16),
                _buildClassTransferInfo(currentClass),
                const SizedBox(height: 16),
                _buildTargetClassInfo(currentStudentsInTarget.length, isClassFull),
                if (isClassFull) ...[
                  const SizedBox(height: 16),
                  _buildWarningCard(),
                ],
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: _isLoading || isClassFull ? null : _addStudentToClass,
              child: _isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Ajouter'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildStudentInfo() {
    return Card(
      color: Colors.blue[50],
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            CircleAvatar(
              backgroundColor: Colors.blue,
              child: Text(
                '${widget.student.firstName[0]}${widget.student.lastName[0]}',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.student.fullName,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    '${widget.student.level.fullName} - ${widget.student.age} ans',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildClassTransferInfo(ClassModel? currentClass) {
    if (currentClass == null) {
      return const Card(
        color: Colors.grey,
        child: Padding(
          padding: EdgeInsets.all(12),
          child: Row(
            children: [
              Icon(Icons.info, color: Colors.white),
              SizedBox(width: 8),
              Text(
                'Élève actuellement sans classe',
                style: TextStyle(color: Colors.white),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      color: Colors.orange[50],
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.swap_horiz, color: Colors.orange[700]),
                const SizedBox(width: 8),
                const Text(
                  'Transfert de classe',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              'De: ${currentClass.name} (${currentClass.level.fullName})',
              style: TextStyle(color: Colors.grey[700]),
            ),
            Text(
              'Vers: ${widget.targetClass.name} (${widget.targetClass.level.fullName})',
              style: TextStyle(color: Colors.grey[700]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTargetClassInfo(int currentCount, bool isClassFull) {
    final color = isClassFull ? Colors.red : Colors.green;
    
    return Card(
      color: color[50],
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.class_, color: color[700]),
                const SizedBox(width: 8),
                Text(
                  'Classe ${widget.targetClass.name}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              'Niveau: ${widget.targetClass.level.fullName}',
              style: TextStyle(color: Colors.grey[700]),
            ),
            Text(
              'Élèves actuels: $currentCount/${widget.targetClass.maxStudents}',
              style: TextStyle(
                color: isClassFull ? Colors.red[700] : Colors.grey[700],
                fontWeight: isClassFull ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            if (widget.targetClass.description != null) ...[
              const SizedBox(height: 4),
              Text(
                'Description: ${widget.targetClass.description}',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildWarningCard() {
    return Card(
      color: Colors.red[50],
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Icon(Icons.warning, color: Colors.red[700]),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'La classe ${widget.targetClass.name} est pleine. Impossible d\'ajouter plus d\'élèves.',
                style: TextStyle(
                  color: Colors.red[700],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _addStudentToClass() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final studentProvider = context.read<StudentProvider>();
      final updatedStudent = widget.student.copyWith(classId: widget.targetClass.id!);
      final success = await studentProvider.updateStudent(updatedStudent);

      if (success) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${widget.student.fullName} a été ajouté à la classe ${widget.targetClass.name}'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'OK',
              onPressed: () {},
            ),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors de l\'ajout de l\'élève à la classe'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
