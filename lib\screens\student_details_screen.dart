import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/student.dart';
import '../models/subject.dart';
import '../models/mark.dart';
import '../providers/mark_provider.dart';
import '../providers/subject_provider.dart';
import '../widgets/mark_form_dialog.dart';

class StudentDetailsScreen extends StatefulWidget {
  final Student student;

  const StudentDetailsScreen({Key? key, required this.student}) : super(key: key);

  @override
  State<StudentDetailsScreen> createState() => _StudentDetailsScreenState();
}

class _StudentDetailsScreenState extends State<StudentDetailsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<MarkProvider>().loadMarks(studentId: widget.student.id);
      context.read<SubjectProvider>().loadSubjects(level: widget.student.level.code);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.student.fullName),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddMarkDialog(),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStudentInfo(),
            const SizedBox(height: 24),
            _buildAverageCard(),
            const SizedBox(height: 24),
            _buildSubjectsAndMarks(),
          ],
        ),
      ),
    );
  }

  Widget _buildStudentInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.blue,
                  child: Text(
                    '${widget.student.firstName[0]}${widget.student.lastName[0]}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.student.fullName,
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${widget.student.level.fullName} - ${widget.student.age} ans',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (widget.student.notes != null) ...[
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 8),
              Text(
                'Notes:',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                widget.student.notes!,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAverageCard() {
    return Consumer<MarkProvider>(
      builder: (context, markProvider, child) {
        final studentMarks = markProvider.getMarksByStudent(widget.student.id!);
        final average = markProvider.getStudentAverage(widget.student.id!);
        
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Moyenne générale',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: _getGradeColor(average),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        average.toStringAsFixed(1),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _getGradeAppreciation(average),
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '${studentMarks.length} note(s) enregistrée(s)',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSubjectsAndMarks() {
    return Consumer2<SubjectProvider, MarkProvider>(
      builder: (context, subjectProvider, markProvider, child) {
        final subjects = subjectProvider.getSubjectsByLevel(widget.student.level);
        
        if (subjects.isEmpty) {
          return const Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Text('Aucune matière disponible pour ce niveau'),
            ),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notes par matière',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...subjects.map((subject) => _buildSubjectCard(subject, markProvider)),
          ],
        );
      },
    );
  }

  Widget _buildSubjectCard(Subject subject, MarkProvider markProvider) {
    final subjectMarks = markProvider.getMarksByStudentAndSubject(
      widget.student.id!,
      subject.id!,
    );
    final subjectAverage = subjectMarks.isNotEmpty
        ? markProvider.getStudentAverage(widget.student.id!, subjectId: subject.id!)
        : 0.0;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Color(int.parse(subject.color.replaceFirst('#', '0xFF'))),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Text(
              subject.code,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ),
        title: Text(
          subject.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          subjectMarks.isNotEmpty
              ? 'Moyenne: ${subjectAverage.toStringAsFixed(1)}/20 (${subjectMarks.length} note(s))'
              : 'Aucune note',
        ),
        trailing: IconButton(
          icon: const Icon(Icons.add),
          onPressed: () => _showAddMarkDialog(preselectedSubject: subject),
        ),
        children: [
          if (subjectMarks.isEmpty)
            const Padding(
              padding: EdgeInsets.all(16),
              child: Text('Aucune note pour cette matière'),
            )
          else
            ...subjectMarks.map((mark) => _buildMarkTile(mark)),
        ],
      ),
    );
  }

  Widget _buildMarkTile(Mark mark) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: _getGradeColor(mark.percentage),
        radius: 20,
        child: Text(
          mark.value.toStringAsFixed(1),
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
        ),
      ),
      title: Text(mark.title ?? mark.type.displayName),
      subtitle: Text(
        '${mark.formattedValue} - ${mark.grade} - ${mark.date.day}/${mark.date.month}/${mark.date.year}',
      ),
      trailing: PopupMenuButton<String>(
        onSelected: (value) {
          switch (value) {
            case 'edit':
              _showEditMarkDialog(mark);
              break;
            case 'delete':
              _showDeleteMarkDialog(mark);
              break;
          }
        },
        itemBuilder: (context) => [
          const PopupMenuItem(
            value: 'edit',
            child: ListTile(
              leading: Icon(Icons.edit),
              title: Text('Modifier'),
            ),
          ),
          const PopupMenuItem(
            value: 'delete',
            child: ListTile(
              leading: Icon(Icons.delete),
              title: Text('Supprimer'),
            ),
          ),
        ],
      ),
    );
  }

  Color _getGradeColor(double value) {
    if (value >= 16) return Colors.green;
    if (value >= 14) return Colors.lightGreen;
    if (value >= 12) return Colors.orange;
    if (value >= 10) return Colors.deepOrange;
    if (value >= 8) return Colors.red;
    return Colors.red[900]!;
  }

  String _getGradeAppreciation(double average) {
    if (average >= 16) return 'Très bien';
    if (average >= 14) return 'Bien';
    if (average >= 12) return 'Assez bien';
    if (average >= 10) return 'Passable';
    if (average >= 8) return 'Insuffisant';
    return 'Très insuffisant';
  }

  void _showAddMarkDialog({Subject? preselectedSubject}) {
    showDialog(
      context: context,
      builder: (context) => MarkFormDialog(
        preselectedStudent: widget.student,
        preselectedSubject: preselectedSubject,
      ),
    );
  }

  void _showEditMarkDialog(Mark mark) {
    showDialog(
      context: context,
      builder: (context) => MarkFormDialog(mark: mark),
    );
  }

  void _showDeleteMarkDialog(Mark mark) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Supprimer la note'),
        content: Text('Êtes-vous sûr de vouloir supprimer cette note de ${mark.formattedValue} ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              context.read<MarkProvider>().deleteMark(mark.id!);
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Note supprimée')),
              );
            },
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );
  }
}
