import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/class_model.dart';
import '../models/student.dart';
import '../providers/student_provider.dart';
import '../providers/class_provider.dart';
import '../widgets/add_student_to_class_dialog.dart';
import '../widgets/student_form_dialog.dart';
import 'student_details_screen.dart';

class ClassDetailsScreen extends StatefulWidget {
  final ClassModel classModel;

  const ClassDetailsScreen({Key? key, required this.classModel}) : super(key: key);

  @override
  State<ClassDetailsScreen> createState() => _ClassDetailsScreenState();
}

class _ClassDetailsScreenState extends State<ClassDetailsScreen> with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<StudentProvider>().loadStudents();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Classe ${widget.classModel.name}'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Élèves de la classe', icon: Icon(Icons.people)),
            Tab(text: 'Ajouter des élèves', icon: Icon(Icons.person_add)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildClassStudentsList(),
          _buildAvailableStudentsList(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showNewStudentDialog(),
        tooltip: 'Créer un nouvel élève',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildClassStudentsList() {
    return Consumer<StudentProvider>(
      builder: (context, studentProvider, child) {
        if (studentProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final classStudents = studentProvider.getStudentsByClass(widget.classModel.id!);

        if (classStudents.isEmpty) {
          return _buildEmptyState(
            icon: Icons.people_outline,
            title: 'Aucun élève dans cette classe',
            subtitle: 'Utilisez l\'onglet "Ajouter des élèves" pour assigner des élèves à cette classe',
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: classStudents.length,
          itemBuilder: (context, index) {
            final student = classStudents[index];
            return _buildStudentCard(
              student: student,
              isInClass: true,
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => StudentDetailsScreen(student: student),
                  ),
                );
              },
              onRemove: () => _showRemoveStudentDialog(student),
            );
          },
        );
      },
    );
  }

  Widget _buildAvailableStudentsList() {
    return Consumer<StudentProvider>(
      builder: (context, studentProvider, child) {
        if (studentProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final availableStudents = studentProvider.students.where((student) {
          // Show students from same level who are either not in any class or in different classes
          return student.level == widget.classModel.level && 
                 student.classId != widget.classModel.id!;
        }).toList();

        if (availableStudents.isEmpty) {
          return _buildEmptyState(
            icon: Icons.person_add_disabled,
            title: 'Aucun élève disponible',
            subtitle: 'Tous les élèves de niveau ${widget.classModel.level.fullName} sont déjà assignés à cette classe ou il n\'y a aucun élève de ce niveau.',
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'Élèves de niveau ${widget.classModel.level.fullName} disponibles',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: availableStudents.length,
                itemBuilder: (context, index) {
                  final student = availableStudents[index];
                  return _buildStudentCard(
                    student: student,
                    isInClass: false,
                    onTap: () => _showAddStudentDialog(student),
                    onAdd: () => _addStudentToClass(student),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildStudentCard({
    required Student student,
    required bool isInClass,
    VoidCallback? onTap,
    VoidCallback? onAdd,
    VoidCallback? onRemove,
  }) {
    return Consumer<ClassProvider>(
      builder: (context, classProvider, child) {
        final studentClass = classProvider.getClassById(student.classId);
        
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: isInClass ? Colors.blue : Colors.green,
              child: Text(
                '${student.firstName[0]}${student.lastName[0]}',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            title: Text(
              student.fullName,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('${student.level.fullName} - ${student.age} ans'),
                if (!isInClass && studentClass != null)
                  Text(
                    'Actuellement en classe: ${studentClass.name}',
                    style: TextStyle(
                      color: Colors.orange[700],
                      fontSize: 12,
                    ),
                  ),
              ],
            ),
            trailing: isInClass
                ? PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'remove':
                          onRemove?.call();
                          break;
                        case 'details':
                          onTap?.call();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'details',
                        child: ListTile(
                          leading: Icon(Icons.visibility),
                          title: Text('Voir détails'),
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'remove',
                        child: ListTile(
                          leading: Icon(Icons.remove_circle),
                          title: Text('Retirer de la classe'),
                        ),
                      ),
                    ],
                  )
                : ElevatedButton.icon(
                    onPressed: onAdd,
                    icon: const Icon(Icons.add, size: 16),
                    label: const Text('Ajouter'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
            onTap: isInClass ? onTap : null,
          ),
        );
      },
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showNewStudentDialog() {
    showDialog(
      context: context,
      builder: (context) => StudentFormDialog(
        initialClass: widget.classModel,
        initialLevel: widget.classModel.level,
      ),
    );
  }

  void _showAddStudentDialog(Student student) {
    showDialog(
      context: context,
      builder: (context) => AddStudentToClassDialog(
        student: student,
        targetClass: widget.classModel,
      ),
    );
  }

  void _addStudentToClass(Student student) async {
    final studentProvider = context.read<StudentProvider>();
    final classProvider = context.read<ClassProvider>();
    
    // Check if class is full
    final currentStudents = studentProvider.getStudentsByClass(widget.classModel.id!);
    if (currentStudents.length >= widget.classModel.maxStudents) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('La classe ${widget.classModel.name} est pleine (${widget.classModel.maxStudents} élèves maximum)'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final updatedStudent = student.copyWith(classId: widget.classModel.id!);
    final success = await studentProvider.updateStudent(updatedStudent);

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${student.fullName} a été ajouté à la classe ${widget.classModel.name}'),
          backgroundColor: Colors.green,
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Erreur lors de l\'ajout de l\'élève à la classe'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showRemoveStudentDialog(Student student) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Retirer l\'élève de la classe'),
        content: Text(
          'Êtes-vous sûr de vouloir retirer "${student.fullName}" de la classe "${widget.classModel.name}" ?\n\n'
          'L\'élève restera dans le système mais ne sera plus assigné à cette classe.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () => _removeStudentFromClass(student),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Retirer'),
          ),
        ],
      ),
    );
  }

  void _removeStudentFromClass(Student student) async {
    Navigator.of(context).pop();
    
    final studentProvider = context.read<StudentProvider>();
    final classProvider = context.read<ClassProvider>();
    
    // Find any available class of the same level to reassign the student
    final availableClasses = classProvider.classes
        .where((c) => c.level == student.level && c.id != widget.classModel.id!)
        .toList();
    
    int? newClassId;
    if (availableClasses.isNotEmpty) {
      // Assign to the first available class of the same level
      newClassId = availableClasses.first.id!;
    } else {
      // If no other class available, we'll need to handle this case
      // For now, we'll show an error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Impossible de retirer l\'élève: aucune autre classe de niveau ${student.level.fullName} n\'existe'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final updatedStudent = student.copyWith(classId: newClassId);
    final success = await studentProvider.updateStudent(updatedStudent);

    if (success) {
      final newClass = classProvider.getClassById(newClassId);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${student.fullName} a été transféré vers la classe ${newClass?.name ?? "inconnue"}'),
          backgroundColor: Colors.orange,
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Erreur lors du retrait de l\'élève de la classe'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
