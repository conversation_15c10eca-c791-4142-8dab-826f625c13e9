import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/class_model.dart';
import '../models/educational_level.dart';
import '../providers/class_provider.dart';

class ClassFormDialog extends StatefulWidget {
  final ClassModel? classModel;

  const ClassFormDialog({Key? key, this.classModel}) : super(key: key);

  @override
  State<ClassFormDialog> createState() => _ClassFormDialogState();
}

class _ClassFormDialogState extends State<ClassFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _maxStudentsController = TextEditingController();
  final _schoolYearController = TextEditingController();

  EducationalLevel _selectedLevel = EducationalLevel.cp;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.classModel != null) {
      _nameController.text = widget.classModel!.name;
      _descriptionController.text = widget.classModel!.description ?? '';
      _maxStudentsController.text = widget.classModel!.maxStudents.toString();
      _schoolYearController.text = widget.classModel!.schoolYear;
      _selectedLevel = widget.classModel!.level;
    } else {
      // Set default school year
      final now = DateTime.now();
      final currentYear = now.year;
      final nextYear = currentYear + 1;
      _schoolYearController.text = '$currentYear-$nextYear';
      _maxStudentsController.text = '30';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _maxStudentsController.dispose();
    _schoolYearController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.classModel == null ? 'Nouvelle classe' : 'Modifier la classe'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'Nom de la classe *',
                    hintText: 'Ex: CM2 A',
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Le nom de la classe est requis';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<EducationalLevel>(
                  value: _selectedLevel,
                  decoration: const InputDecoration(
                    labelText: 'Niveau *',
                  ),
                  items: EducationalLevel.values.map((level) {
                    return DropdownMenuItem(
                      value: level,
                      child: Text('${level.code} - ${level.fullName}'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedLevel = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _schoolYearController,
                  decoration: const InputDecoration(
                    labelText: 'Année scolaire *',
                    hintText: 'Ex: 2023-2024',
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'L\'année scolaire est requise';
                    }
                    // Basic validation for format YYYY-YYYY
                    final regex = RegExp(r'^\d{4}-\d{4}$');
                    if (!regex.hasMatch(value.trim())) {
                      return 'Format invalide (ex: 2023-2024)';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _maxStudentsController,
                  decoration: const InputDecoration(
                    labelText: 'Nombre maximum d\'élèves',
                    hintText: '30',
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Le nombre maximum d\'élèves est requis';
                    }
                    final number = int.tryParse(value.trim());
                    if (number == null || number <= 0) {
                      return 'Veuillez entrer un nombre valide';
                    }
                    if (number > 50) {
                      return 'Le nombre maximum ne peut pas dépasser 50';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description (optionnel)',
                    hintText: 'Description de la classe...',
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveClass,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(widget.classModel == null ? 'Créer' : 'Modifier'),
        ),
      ],
    );
  }

  Future<void> _saveClass() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final classModel = ClassModel(
        id: widget.classModel?.id,
        name: _nameController.text.trim(),
        level: _selectedLevel,
        schoolYear: _schoolYearController.text.trim(),
        description: _descriptionController.text.trim().isEmpty 
            ? null 
            : _descriptionController.text.trim(),
        maxStudents: int.parse(_maxStudentsController.text.trim()),
        createdAt: widget.classModel?.createdAt,
      );

      final classProvider = context.read<ClassProvider>();
      bool success;

      if (widget.classModel == null) {
        success = await classProvider.addClass(classModel);
      } else {
        success = await classProvider.updateClass(classModel);
      }

      if (success) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.classModel == null 
                ? 'Classe créée avec succès' 
                : 'Classe modifiée avec succès'),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors de la sauvegarde'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
