enum EducationalLevel {
  // École primaire
  cp('CP', 'Cours Préparatoire', 'primaire'),
  ce1('CE1', 'Cours Élémentaire 1ère année', 'primaire'),
  ce2('CE2', 'Cours Élémentaire 2ème année', 'primaire'),
  cm1('CM1', 'Cours Moyen 1ère année', 'primaire'),
  cm2('CM2', 'Cours Moyen 2ème année', 'primaire'),
  
  // Collège
  sixieme('6ème', 'Sixième', 'collège'),
  cinquieme('5ème', 'Cinquième', 'collège'),
  quatrieme('4ème', 'Quatrième', 'collège'),
  troisieme('3ème', 'Troisième', 'collège'),
  
  // Lycée
  seconde('2nde', 'Seconde', 'lycée'),
  premiere('1ère', 'Première', 'lycée'),
  terminale('Term', 'Terminale', 'lycée');

  const EducationalLevel(this.code, this.fullName, this.cycle);
  
  final String code;
  final String fullName;
  final String cycle;
  
  static EducationalLevel fromString(String value) {
    return EducationalLevel.values.firstWhere(
      (level) => level.code == value,
      orElse: () => EducationalLevel.cp,
    );
  }
  
  static List<EducationalLevel> getLevelsByCycle(String cycle) {
    return EducationalLevel.values
        .where((level) => level.cycle == cycle)
        .toList();
  }
  
  static List<String> getAllCycles() {
    return ['primaire', 'collège', 'lycée'];
  }
}
