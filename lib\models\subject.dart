import 'educational_level.dart';

class Subject {
  final int? id;
  final String name;
  final String code; // e.g., "MATH", "FRAN", "HIST"
  final EducationalLevel level;
  final String? description;
  final int coefficient; // Coefficient for grade calculation
  final String color; // Hex color for UI
  final DateTime createdAt;
  final DateTime updatedAt;

  Subject({
    this.id,
    required this.name,
    required this.code,
    required this.level,
    this.description,
    this.coefficient = 1,
    this.color = '#2196F3',
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'level': level.code,
      'description': description,
      'coefficient': coefficient,
      'color': color,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory Subject.fromMap(Map<String, dynamic> map) {
    return Subject(
      id: map['id'],
      name: map['name'],
      code: map['code'],
      level: EducationalLevel.fromString(map['level']),
      description: map['description'],
      coefficient: map['coefficient'] ?? 1,
      color: map['color'] ?? '#2196F3',
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }

  Subject copyWith({
    int? id,
    String? name,
    String? code,
    EducationalLevel? level,
    String? description,
    int? coefficient,
    String? color,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Subject(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      level: level ?? this.level,
      description: description ?? this.description,
      coefficient: coefficient ?? this.coefficient,
      color: color ?? this.color,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  // Predefined subjects for French educational system
  static List<Subject> getDefaultSubjects(EducationalLevel level) {
    switch (level.cycle) {
      case 'primaire':
        return [
          Subject(name: 'Français', code: 'FRAN', level: level, color: '#E91E63'),
          Subject(name: 'Mathématiques', code: 'MATH', level: level, color: '#2196F3'),
          Subject(name: 'Histoire-Géographie', code: 'HIST', level: level, color: '#FF9800'),
          Subject(name: 'Sciences', code: 'SCI', level: level, color: '#4CAF50'),
          Subject(name: 'Arts plastiques', code: 'ART', level: level, color: '#9C27B0'),
          Subject(name: 'Éducation physique', code: 'EPS', level: level, color: '#FF5722'),
        ];
      case 'collège':
        return [
          Subject(name: 'Français', code: 'FRAN', level: level, color: '#E91E63', coefficient: 4),
          Subject(name: 'Mathématiques', code: 'MATH', level: level, color: '#2196F3', coefficient: 4),
          Subject(name: 'Histoire-Géographie', code: 'HIST', level: level, color: '#FF9800', coefficient: 3),
          Subject(name: 'Sciences de la vie et de la Terre', code: 'SVT', level: level, color: '#4CAF50', coefficient: 2),
          Subject(name: 'Physique-Chimie', code: 'PC', level: level, color: '#00BCD4', coefficient: 2),
          Subject(name: 'Anglais', code: 'ANG', level: level, color: '#795548', coefficient: 3),
          Subject(name: 'Arts plastiques', code: 'ART', level: level, color: '#9C27B0', coefficient: 1),
          Subject(name: 'Éducation physique', code: 'EPS', level: level, color: '#FF5722', coefficient: 1),
        ];
      case 'lycée':
        return [
          Subject(name: 'Français', code: 'FRAN', level: level, color: '#E91E63', coefficient: 4),
          Subject(name: 'Mathématiques', code: 'MATH', level: level, color: '#2196F3', coefficient: 4),
          Subject(name: 'Histoire-Géographie', code: 'HIST', level: level, color: '#FF9800', coefficient: 3),
          Subject(name: 'Philosophie', code: 'PHIL', level: level, color: '#673AB7', coefficient: 3),
          Subject(name: 'Sciences de la vie et de la Terre', code: 'SVT', level: level, color: '#4CAF50', coefficient: 3),
          Subject(name: 'Physique-Chimie', code: 'PC', level: level, color: '#00BCD4', coefficient: 3),
          Subject(name: 'Anglais', code: 'ANG', level: level, color: '#795548', coefficient: 3),
          Subject(name: 'Éducation physique', code: 'EPS', level: level, color: '#FF5722', coefficient: 2),
        ];
      default:
        return [];
    }
  }
}
