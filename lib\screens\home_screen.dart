import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/class_provider.dart';
import '../providers/student_provider.dart';
import '../providers/subject_provider.dart';
import '../providers/mark_provider.dart';
import 'classes_screen.dart';
import 'students_screen.dart';
import 'subjects_screen.dart';
import 'marks_screen.dart';
import 'reports_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;

  List<Widget> get _screens => [
    DashboardTab(onNavigateToTab: (index) {
      setState(() {
        _selectedIndex = index;
      });
    }),
    const ClassesScreen(),
    const StudentsScreen(),
    const SubjectsScreen(),
    const MarksScreen(),
    const ReportsScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestion de Classe'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: _screens[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Tableau de bord',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.class_),
            label: 'Classes',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.people),
            label: 'Élèves',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.subject),
            label: 'Matières',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.grade),
            label: 'Notes',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.assessment),
            label: 'Rapports',
          ),
        ],
      ),
    );
  }
}

class DashboardTab extends StatelessWidget {
  final Function(int) onNavigateToTab;
  
  const DashboardTab({Key? key, required this.onNavigateToTab}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Bienvenue dans votre application de gestion de classe',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 24),
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                _buildDashboardCard(
                  context,
                  'Classes',
                  Icons.class_,
                  Colors.blue,
                  () => onNavigateToTab(1),
                ),
                _buildDashboardCard(
                  context,
                  'Élèves',
                  Icons.people,
                  Colors.green,
                  () => onNavigateToTab(2),
                ),
                _buildDashboardCard(
                  context,
                  'Matières',
                  Icons.subject,
                  Colors.orange,
                  () => onNavigateToTab(3),
                ),
                _buildDashboardCard(
                  context,
                  'Notes',
                  Icons.grade,
                  Colors.purple,
                  () => onNavigateToTab(4),
                ),
                _buildDashboardCard(
                  context,
                  'Rapports',
                  Icons.assessment,
                  Colors.red,
                  () => onNavigateToTab(5),
                ),
                _buildDashboardCard(
                  context,
                  'Statistiques',
                  Icons.analytics,
                  Colors.teal,
                  () => _showStatistics(context),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: color,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }


  void _showStatistics(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Statistiques'),
        content: Consumer4<ClassProvider, StudentProvider, SubjectProvider, MarkProvider>(
          builder: (context, classProvider, studentProvider, subjectProvider, markProvider, child) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Classes: ${classProvider.classes.length}'),
                Text('Élèves: ${studentProvider.students.length}'),
                Text('Matières: ${subjectProvider.subjects.length}'),
                Text('Notes: ${markProvider.marks.length}'),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }
}
