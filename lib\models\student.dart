import 'educational_level.dart';

class Student {
  final int? id;
  final String firstName;
  final String lastName;
  final DateTime dateOfBirth;
  final EducationalLevel level;
  final int classId;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  Student({
    this.id,
    required this.firstName,
    required this.lastName,
    required this.dateOfBirth,
    required this.level,
    required this.classId,
    this.notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  String get fullName => '$firstName $lastName';
  
  int get age {
    final now = DateTime.now();
    int age = now.year - dateOfBirth.year;
    if (now.month < dateOfBirth.month || 
        (now.month == dateOfBirth.month && now.day < dateOfBirth.day)) {
      age--;
    }
    return age;
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'firstName': firstName,
      'lastName': lastName,
      'dateOfBirth': dateOfBirth.toIso8601String(),
      'level': level.code,
      'classId': classId,
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory Student.fromMap(Map<String, dynamic> map) {
    return Student(
      id: map['id'],
      firstName: map['firstName'],
      lastName: map['lastName'],
      dateOfBirth: DateTime.parse(map['dateOfBirth']),
      level: EducationalLevel.fromString(map['level']),
      classId: map['classId'],
      notes: map['notes'],
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }

  Student copyWith({
    int? id,
    String? firstName,
    String? lastName,
    DateTime? dateOfBirth,
    EducationalLevel? level,
    int? classId,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Student(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      level: level ?? this.level,
      classId: classId ?? this.classId,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }
}
