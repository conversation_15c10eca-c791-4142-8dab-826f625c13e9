import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/mark.dart';
import '../models/student.dart';
import '../models/subject.dart';
import '../providers/mark_provider.dart';
import '../providers/student_provider.dart';
import '../providers/subject_provider.dart';

class MarkFormDialog extends StatefulWidget {
  final Mark? mark;
  final Student? preselectedStudent;
  final Subject? preselectedSubject;

  const MarkFormDialog({
    Key? key,
    this.mark,
    this.preselectedStudent,
    this.preselectedSubject,
  }) : super(key: key);

  @override
  State<MarkFormDialog> createState() => _MarkFormDialogState();
}

class _MarkFormDialogState extends State<MarkFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _valueController = TextEditingController();
  final _maxValueController = TextEditingController();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();

  Student? _selectedStudent;
  Subject? _selectedSubject;
  MarkType _selectedType = MarkType.evaluation;
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    
    if (widget.mark != null) {
      _valueController.text = widget.mark!.value.toString();
      _maxValueController.text = widget.mark!.maxValue.toString();
      _titleController.text = widget.mark!.title ?? '';
      _descriptionController.text = widget.mark!.description ?? '';
      _selectedType = widget.mark!.type;
      _selectedDate = widget.mark!.date;
      
      // Find student and subject
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final studentProvider = context.read<StudentProvider>();
        final subjectProvider = context.read<SubjectProvider>();
        
        _selectedStudent = studentProvider.getStudentById(widget.mark!.studentId);
        _selectedSubject = subjectProvider.getSubjectById(widget.mark!.subjectId);
        setState(() {});
      });
    } else {
      _maxValueController.text = '20';
      _selectedStudent = widget.preselectedStudent;
      _selectedSubject = widget.preselectedSubject;
    }
  }

  @override
  void dispose() {
    _valueController.dispose();
    _maxValueController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.mark == null ? 'Nouvelle note' : 'Modifier la note'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Student selection
                Consumer<StudentProvider>(
                  builder: (context, studentProvider, child) {
                    if (studentProvider.students.isEmpty) {
                      return const Text('Aucun élève disponible');
                    }

                    return DropdownButtonFormField<Student>(
                      value: _selectedStudent,
                      decoration: const InputDecoration(
                        labelText: 'Élève *',
                      ),
                      items: studentProvider.students.map((student) {
                        return DropdownMenuItem(
                          value: student,
                          child: Text(student.fullName),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedStudent = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'Veuillez sélectionner un élève';
                        }
                        return null;
                      },
                    );
                  },
                ),
                const SizedBox(height: 16),
                
                // Subject selection
                Consumer<SubjectProvider>(
                  builder: (context, subjectProvider, child) {
                    final availableSubjects = _selectedStudent != null
                        ? subjectProvider.subjects
                            .where((s) => s.level == _selectedStudent!.level)
                            .toList()
                        : subjectProvider.subjects;

                    if (availableSubjects.isEmpty) {
                      return const Text('Aucune matière disponible');
                    }

                    return DropdownButtonFormField<Subject>(
                      value: _selectedSubject,
                      decoration: const InputDecoration(
                        labelText: 'Matière *',
                      ),
                      items: availableSubjects.map((subject) {
                        return DropdownMenuItem(
                          value: subject,
                          child: Row(
                            children: [
                              Container(
                                width: 16,
                                height: 16,
                                decoration: BoxDecoration(
                                  color: Color(int.parse(subject.color.replaceFirst('#', '0xFF'))),
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(subject.name),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedSubject = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'Veuillez sélectionner une matière';
                        }
                        return null;
                      },
                    );
                  },
                ),
                const SizedBox(height: 16),
                
                // Mark type
                DropdownButtonFormField<MarkType>(
                  value: _selectedType,
                  decoration: const InputDecoration(
                    labelText: 'Type d\'évaluation *',
                  ),
                  items: MarkType.values.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(type.displayName),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedType = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),
                
                // Mark value and max value
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: TextFormField(
                        controller: _valueController,
                        decoration: const InputDecoration(
                          labelText: 'Note *',
                          hintText: '15.5',
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'La note est requise';
                          }
                          final number = double.tryParse(value.trim());
                          if (number == null || number < 0) {
                            return 'Note invalide';
                          }
                          final maxValue = double.tryParse(_maxValueController.text.trim()) ?? 20;
                          if (number > maxValue) {
                            return 'Note > maximum';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      flex: 1,
                      child: TextFormField(
                        controller: _maxValueController,
                        decoration: const InputDecoration(
                          labelText: 'Sur',
                          hintText: '20',
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Requis';
                          }
                          final number = double.tryParse(value.trim());
                          if (number == null || number <= 0) {
                            return 'Invalide';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                
                // Date
                InkWell(
                  onTap: _selectDate,
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      labelText: 'Date *',
                      suffixIcon: Icon(Icons.calendar_today),
                    ),
                    child: Text(
                      '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                
                // Title
                TextFormField(
                  controller: _titleController,
                  decoration: const InputDecoration(
                    labelText: 'Titre (optionnel)',
                    hintText: 'Ex: Contrôle de mathématiques',
                  ),
                ),
                const SizedBox(height: 16),
                
                // Description
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description (optionnel)',
                    hintText: 'Commentaires sur l\'évaluation...',
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveMark,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(widget.mark == null ? 'Créer' : 'Modifier'),
        ),
      ],
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 30)),
      locale: const Locale('fr', 'FR'),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _saveMark() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedStudent == null || _selectedSubject == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez sélectionner un élève et une matière'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final mark = Mark(
        id: widget.mark?.id,
        studentId: _selectedStudent!.id!,
        subjectId: _selectedSubject!.id!,
        value: double.parse(_valueController.text.trim()),
        maxValue: double.parse(_maxValueController.text.trim()),
        type: _selectedType,
        title: _titleController.text.trim().isEmpty 
            ? null 
            : _titleController.text.trim(),
        description: _descriptionController.text.trim().isEmpty 
            ? null 
            : _descriptionController.text.trim(),
        date: _selectedDate,
        createdAt: widget.mark?.createdAt,
      );

      final markProvider = context.read<MarkProvider>();
      bool success;

      if (widget.mark == null) {
        success = await markProvider.addMark(mark);
      } else {
        success = await markProvider.updateMark(mark);
      }

      if (success) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.mark == null 
                ? 'Note créée avec succès' 
                : 'Note modifiée avec succès'),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors de la sauvegarde'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
