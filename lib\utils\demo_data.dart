import '../models/class_model.dart';
import '../models/student.dart';
import '../models/subject.dart';
import '../models/mark.dart';
import '../models/educational_level.dart';
import '../services/database_service.dart';

class DemoData {
  static Future<void> populateDatabase(DatabaseService databaseService) async {
    // Create demo classes
    final classes = await _createDemoClasses(databaseService);
    
    // Create demo subjects
    await _createDemoSubjects(databaseService);
    
    // Create demo students
    await _createDemoStudents(databaseService, classes);
    
    print('Demo data populated successfully!');
  }

  static Future<List<ClassModel>> _createDemoClasses(DatabaseService databaseService) async {
    final classes = <ClassModel>[];
    
    // Primaire classes
    final cp = ClassModel(
      name: 'CP A',
      level: EducationalLevel.cp,
      schoolYear: '2023-2024',
      description: 'Classe de CP avec 25 élèves',
      maxStudents: 25,
    );
    final cpId = await databaseService.insertClass(cp);
    classes.add(cp.copyWith(id: cpId));

    final ce1 = ClassModel(
      name: 'CE1 B',
      level: EducationalLevel.ce1,
      schoolYear: '2023-2024',
      description: 'Classe de CE1 dynamique',
      maxStudents: 26,
    );
    final ce1Id = await databaseService.insertClass(ce1);
    classes.add(ce1.copyWith(id: ce1Id));

    final cm2 = ClassModel(
      name: 'CM2 A',
      level: EducationalLevel.cm2,
      schoolYear: '2023-2024',
      description: 'Classe de CM2 préparation 6ème',
      maxStudents: 28,
    );
    final cm2Id = await databaseService.insertClass(cm2);
    classes.add(cm2.copyWith(id: cm2Id));

    // Collège classes
    final sixieme = ClassModel(
      name: '6ème 1',
      level: EducationalLevel.sixieme,
      schoolYear: '2023-2024',
      description: 'Classe de 6ème générale',
      maxStudents: 30,
    );
    final sixiemeId = await databaseService.insertClass(sixieme);
    classes.add(sixieme.copyWith(id: sixiemeId));

    final troisieme = ClassModel(
      name: '3ème 2',
      level: EducationalLevel.troisieme,
      schoolYear: '2023-2024',
      description: 'Classe de 3ème préparation brevet',
      maxStudents: 28,
    );
    final troisiemeId = await databaseService.insertClass(troisieme);
    classes.add(troisieme.copyWith(id: troisiemeId));

    // Lycée classes
    final seconde = ClassModel(
      name: '2nde 3',
      level: EducationalLevel.seconde,
      schoolYear: '2023-2024',
      description: 'Classe de seconde générale',
      maxStudents: 35,
    );
    final secondeId = await databaseService.insertClass(seconde);
    classes.add(seconde.copyWith(id: secondeId));

    return classes;
  }

  static Future<void> _createDemoSubjects(DatabaseService databaseService) async {
    // Create subjects for different levels
    final levels = [
      EducationalLevel.cp,
      EducationalLevel.ce1,
      EducationalLevel.cm2,
      EducationalLevel.sixieme,
      EducationalLevel.troisieme,
      EducationalLevel.seconde,
    ];

    for (final level in levels) {
      final subjects = Subject.getDefaultSubjects(level);
      for (final subject in subjects) {
        await databaseService.insertSubject(subject);
      }
    }
  }

  static Future<void> _createDemoStudents(DatabaseService databaseService, List<ClassModel> classes) async {
    final studentNames = [
      ['Emma', 'Martin'],
      ['Lucas', 'Bernard'],
      ['Léa', 'Dubois'],
      ['Hugo', 'Thomas'],
      ['Chloé', 'Robert'],
      ['Louis', 'Petit'],
      ['Manon', 'Richard'],
      ['Nathan', 'Durand'],
      ['Camille', 'Leroy'],
      ['Enzo', 'Moreau'],
      ['Inès', 'Simon'],
      ['Gabriel', 'Laurent'],
      ['Jade', 'Lefebvre'],
      ['Raphaël', 'Michel'],
      ['Lola', 'Garcia'],
      ['Arthur', 'David'],
      ['Zoé', 'Bertrand'],
      ['Théo', 'Roux'],
      ['Clara', 'Vincent'],
      ['Maxime', 'Fournier'],
    ];

    for (int i = 0; i < classes.length; i++) {
      final classModel = classes[i];
      final studentsPerClass = (classModel.maxStudents * 0.8).round(); // 80% capacity
      
      for (int j = 0; j < studentsPerClass && j < studentNames.length; j++) {
        final nameIndex = (i * studentsPerClass + j) % studentNames.length;
        final name = studentNames[nameIndex];
        
        // Calculate birth date based on level
        final baseAge = _getBaseAgeForLevel(classModel.level);
        final birthYear = DateTime.now().year - baseAge;
        final birthDate = DateTime(
          birthYear,
          DateTime.now().month + (j % 12) - 6, // Spread across months
          1 + (j % 28), // Spread across days
        );

        final student = Student(
          firstName: name[0],
          lastName: name[1],
          dateOfBirth: birthDate,
          level: classModel.level,
          classId: classModel.id!,
          notes: j % 3 == 0 ? 'Élève très motivé' : null,
        );

        await databaseService.insertStudent(student);
      }
    }
  }

  static int _getBaseAgeForLevel(EducationalLevel level) {
    switch (level) {
      case EducationalLevel.cp:
        return 6;
      case EducationalLevel.ce1:
        return 7;
      case EducationalLevel.ce2:
        return 8;
      case EducationalLevel.cm1:
        return 9;
      case EducationalLevel.cm2:
        return 10;
      case EducationalLevel.sixieme:
        return 11;
      case EducationalLevel.cinquieme:
        return 12;
      case EducationalLevel.quatrieme:
        return 13;
      case EducationalLevel.troisieme:
        return 14;
      case EducationalLevel.seconde:
        return 15;
      case EducationalLevel.premiere:
        return 16;
      case EducationalLevel.terminale:
        return 17;
    }
  }

  static Future<void> createDemoMarks(DatabaseService databaseService) async {
    // Get all students and subjects
    final students = await databaseService.getStudents();
    final subjects = await databaseService.getSubjects();

    for (final student in students) {
      // Get subjects for this student's level
      final studentSubjects = subjects.where((s) => s.level == student.level).toList();
      
      for (final subject in studentSubjects) {
        // Create 3-5 marks per subject per student
        final markCount = 3 + (student.id! % 3);
        
        for (int i = 0; i < markCount; i++) {
          final mark = Mark(
            studentId: student.id!,
            subjectId: subject.id!,
            value: _generateRandomMark(),
            type: MarkType.values[i % MarkType.values.length],
            title: _getMarkTitle(subject.name, i),
            date: DateTime.now().subtract(Duration(days: i * 7 + (student.id! % 30))),
          );

          await databaseService.insertMark(mark);
        }
      }
    }
  }

  static double _generateRandomMark() {
    // Generate marks with realistic distribution
    final random = DateTime.now().millisecondsSinceEpoch % 100;
    if (random < 10) return 8.0 + (random % 4); // 8-12 (10%)
    if (random < 30) return 12.0 + (random % 4); // 12-16 (20%)
    if (random < 70) return 14.0 + (random % 4); // 14-18 (40%)
    return 16.0 + (random % 4); // 16-20 (30%)
  }

  static String _getMarkTitle(String subjectName, int index) {
    final titles = [
      'Évaluation $subjectName',
      'Devoir surveillé',
      'Contrôle continu',
      'Interrogation écrite',
      'Exercice pratique',
    ];
    return titles[index % titles.length];
  }
}
