import 'educational_level.dart';

class ClassModel {
  final int? id;
  final String name;
  final EducationalLevel level;
  final String schoolYear; // e.g., "2023-2024"
  final String? description;
  final int maxStudents;
  final DateTime createdAt;
  final DateTime updatedAt;

  ClassModel({
    this.id,
    required this.name,
    required this.level,
    required this.schoolYear,
    this.description,
    this.maxStudents = 30,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'level': level.code,
      'schoolYear': schoolYear,
      'description': description,
      'maxStudents': maxStudents,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory ClassModel.fromMap(Map<String, dynamic> map) {
    return ClassModel(
      id: map['id'],
      name: map['name'],
      level: EducationalLevel.fromString(map['level']),
      schoolYear: map['schoolYear'],
      description: map['description'],
      maxStudents: map['maxStudents'] ?? 30,
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }

  ClassModel copyWith({
    int? id,
    String? name,
    EducationalLevel? level,
    String? schoolYear,
    String? description,
    int? maxStudents,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ClassModel(
      id: id ?? this.id,
      name: name ?? this.name,
      level: level ?? this.level,
      schoolYear: schoolYear ?? this.schoolYear,
      description: description ?? this.description,
      maxStudents: maxStudents ?? this.maxStudents,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }
}
