import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/subject.dart';
import '../models/educational_level.dart';
import '../providers/subject_provider.dart';

class SubjectFormDialog extends StatefulWidget {
  final Subject? subject;

  const SubjectFormDialog({Key? key, this.subject}) : super(key: key);

  @override
  State<SubjectFormDialog> createState() => _SubjectFormDialogState();
}

class _SubjectFormDialogState extends State<SubjectFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _codeController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _coefficientController = TextEditingController();

  EducationalLevel _selectedLevel = EducationalLevel.cp;
  Color _selectedColor = Colors.blue;
  bool _isLoading = false;

  final List<Color> _availableColors = [
    Colors.blue,
    Colors.red,
    Colors.green,
    Colors.orange,
    Colors.purple,
    Colors.teal,
    Colors.pink,
    Colors.indigo,
    Colors.brown,
    Colors.cyan,
  ];

  @override
  void initState() {
    super.initState();
    if (widget.subject != null) {
      _nameController.text = widget.subject!.name;
      _codeController.text = widget.subject!.code;
      _descriptionController.text = widget.subject!.description ?? '';
      _coefficientController.text = widget.subject!.coefficient.toString();
      _selectedLevel = widget.subject!.level;
      _selectedColor = Color(int.parse(widget.subject!.color.replaceFirst('#', '0xFF')));
    } else {
      _coefficientController.text = '1';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _codeController.dispose();
    _descriptionController.dispose();
    _coefficientController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.subject == null ? 'Nouvelle matière' : 'Modifier la matière'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'Nom de la matière *',
                    hintText: 'Ex: Mathématiques',
                  ),
                  textCapitalization: TextCapitalization.words,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Le nom de la matière est requis';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _codeController,
                  decoration: const InputDecoration(
                    labelText: 'Code matière *',
                    hintText: 'Ex: MATH',
                  ),
                  textCapitalization: TextCapitalization.characters,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Le code matière est requis';
                    }
                    if (value.trim().length > 6) {
                      return 'Le code ne peut pas dépasser 6 caractères';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<EducationalLevel>(
                  value: _selectedLevel,
                  decoration: const InputDecoration(
                    labelText: 'Niveau *',
                  ),
                  items: EducationalLevel.values.map((level) {
                    return DropdownMenuItem(
                      value: level,
                      child: Text('${level.code} - ${level.fullName}'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedLevel = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _coefficientController,
                  decoration: const InputDecoration(
                    labelText: 'Coefficient',
                    hintText: '1',
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Le coefficient est requis';
                    }
                    final number = int.tryParse(value.trim());
                    if (number == null || number <= 0) {
                      return 'Veuillez entrer un nombre valide';
                    }
                    if (number > 10) {
                      return 'Le coefficient ne peut pas dépasser 10';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                const Align(
                  alignment: Alignment.centerLeft,
                  child: Text('Couleur de la matière'),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  children: _availableColors.map((color) {
                    final isSelected = color.value == _selectedColor.value;
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedColor = color;
                        });
                      },
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: color,
                          shape: BoxShape.circle,
                          border: isSelected
                              ? Border.all(color: Colors.black, width: 3)
                              : null,
                        ),
                        child: isSelected
                            ? const Icon(Icons.check, color: Colors.white)
                            : null,
                      ),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description (optionnel)',
                    hintText: 'Description de la matière...',
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveSubject,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(widget.subject == null ? 'Créer' : 'Modifier'),
        ),
      ],
    );
  }

  Future<void> _saveSubject() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final subject = Subject(
        id: widget.subject?.id,
        name: _nameController.text.trim(),
        code: _codeController.text.trim().toUpperCase(),
        level: _selectedLevel,
        description: _descriptionController.text.trim().isEmpty 
            ? null 
            : _descriptionController.text.trim(),
        coefficient: int.parse(_coefficientController.text.trim()),
        color: '#${_selectedColor.value.toRadixString(16).substring(2).toUpperCase()}',
        createdAt: widget.subject?.createdAt,
      );

      final subjectProvider = context.read<SubjectProvider>();
      bool success;

      if (widget.subject == null) {
        success = await subjectProvider.addSubject(subject);
      } else {
        success = await subjectProvider.updateSubject(subject);
      }

      if (success) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.subject == null 
                ? 'Matière créée avec succès' 
                : 'Matière modifiée avec succès'),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors de la sauvegarde'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
