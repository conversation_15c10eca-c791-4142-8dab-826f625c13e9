import 'package:flutter/foundation.dart';
import '../models/student.dart';
import '../services/database_service.dart';

class StudentProvider with ChangeNotifier {
  final DatabaseService _databaseService;
  List<Student> _students = [];
  Student? _selectedStudent;
  bool _isLoading = false;

  StudentProvider(this._databaseService);

  List<Student> get students => _students;
  Student? get selectedStudent => _selectedStudent;
  bool get isLoading => _isLoading;

  Future<void> loadStudents({int? classId}) async {
    _isLoading = true;
    notifyListeners();

    try {
      _students = await _databaseService.getStudents(classId: classId);
    } catch (e) {
      debugPrint('Error loading students: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> addStudent(Student student) async {
    try {
      final id = await _databaseService.insertStudent(student);
      final newStudent = student.copyWith(id: id);
      _students.add(newStudent);
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error adding student: $e');
      return false;
    }
  }

  Future<bool> updateStudent(Student student) async {
    try {
      await _databaseService.updateStudent(student);
      final index = _students.indexWhere((s) => s.id == student.id);
      if (index != -1) {
        _students[index] = student;
        if (_selectedStudent?.id == student.id) {
          _selectedStudent = student;
        }
        notifyListeners();
      }
      return true;
    } catch (e) {
      debugPrint('Error updating student: $e');
      return false;
    }
  }

  Future<bool> deleteStudent(int id) async {
    try {
      await _databaseService.deleteStudent(id);
      _students.removeWhere((s) => s.id == id);
      if (_selectedStudent?.id == id) {
        _selectedStudent = null;
      }
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error deleting student: $e');
      return false;
    }
  }

  void selectStudent(Student? student) {
    _selectedStudent = student;
    notifyListeners();
  }

  Student? getStudentById(int id) {
    try {
      return _students.firstWhere((s) => s.id == id);
    } catch (e) {
      return null;
    }
  }

  List<Student> getStudentsByClass(int classId) {
    return _students.where((s) => s.classId == classId).toList();
  }

  List<Student> getStudentsByLevel(String level) {
    return _students.where((s) => s.level.code == level).toList();
  }

  int getStudentCountByClass(int classId) {
    return _students.where((s) => s.classId == classId).length;
  }
}
